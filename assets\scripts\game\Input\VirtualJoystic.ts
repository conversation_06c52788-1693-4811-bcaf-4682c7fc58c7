import { _decorator, Component, Node, Vec3, input, Input, EventMouse, Vec2, EventTouch, CCFloat, UITransform, Canvas, find } from "cc";
import { IInput } from "./IInput";
const { ccclass, property } = _decorator;

@ccclass("VirtualJoystic")
export class Virtual<PERSON>oy<PERSON> extends Component implements IInput {
    @property(CCFloat) private maxDistance = 10;
    @property(Node) private knob: Node;

    #isUsingJoystic = false;
    #defaultPosition: Vec2 = new Vec2();
    #canvasUITransform: UITransform = null;

    start() {
        this.init();
    }

    public init(): void {
        // Get Canvas UITransform for coordinate conversion
        const canvasNode = find("Canvas");
        if (canvasNode) {
            this.#canvasUITransform = canvasNode.getComponent(UITransform);
        }

        input.on(Input.EventType.MOUSE_DOWN, this.activateMouseJoystic, this);
        input.on(Input.EventType.MOUSE_UP, this.deactivateJoystic, this);
        input.on(Input.EventType.MOUSE_MOVE, this.moveKnobMouse, this);

        input.on(Input.EventType.TOUCH_START, this.activateTouchJoystic, this);
        input.on(Input.EventType.TOUCH_END, this.deactivateJoystic, this);
        input.on(Input.EventType.TOUCH_MOVE, this.moveKnobTouch, this);

        this.deactivateJoystic();
    }

    public getAxis(): Vec2 {
        if (this.#isUsingJoystic) {
            return new Vec2(this.knob.position.x / this.maxDistance, this.knob.position.y / this.maxDistance);
        } else {
            return new Vec2();
        }
    }

    private activateTouchJoystic(e: EventTouch): void {
        this.activateJoystic(e.getLocation());
    }

    private activateMouseJoystic(e: EventMouse): void {
        console.log("Mouse location:", e.getLocation());
        console.log("Mouse UI location:", e.getUILocation());
        this.activateJoystic(e.getLocation());
    }

    private convertScreenToCanvasPosition(screenPos: Vec2): Vec2 {
        if (!this.#canvasUITransform) {
            return screenPos;
        }

        // Convert screen position to Canvas local position
        const canvasPos = new Vec3();
        this.#canvasUITransform.convertToNodeSpaceAR(new Vec3(screenPos.x, screenPos.y, 0), canvasPos);

        return new Vec2(canvasPos.x, canvasPos.y);
    }

    private activateJoystic(location: Vec2): void {
        this.#isUsingJoystic = true;
        this.node.active = true;

        // Convert screen coordinates to Canvas local coordinates
        const canvasPosition = this.convertScreenToCanvasPosition(location);
        this.#defaultPosition = canvasPosition;

        this.node.setPosition(new Vec3(this.#defaultPosition.x, this.#defaultPosition.y, 0));
        this.knob.position = new Vec3();
    }

    private deactivateJoystic(): void {
        this.#isUsingJoystic = false;
        this.node.active = false;
    }

    private moveKnobTouch(e: EventTouch): void {
        this.moveKnob(e.getUILocation());
    }

    private moveKnobMouse(e: EventMouse): void {
        this.moveKnob(e.getUILocation());
    }

    private moveKnob(location: Vec2): void {
        if (!this.#isUsingJoystic) return;

        // Convert screen coordinates to Canvas local coordinates
        const canvasPosition = this.convertScreenToCanvasPosition(location);
        const posDelta: Vec2 = canvasPosition.subtract(this.#defaultPosition);
        let x: number = posDelta.x;
        let y: number = posDelta.y;

        const length: number = Math.sqrt(posDelta.x ** 2 + posDelta.y ** 2);
        if (this.maxDistance < length) {
            const multiplier: number = this.maxDistance / length;

            x *= multiplier;
            y *= multiplier;
        }

        this.knob.position = new Vec3(x, y, 0);
    }
}
