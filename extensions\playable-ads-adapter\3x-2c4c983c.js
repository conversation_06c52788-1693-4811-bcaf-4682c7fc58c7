"use strict";var e=require("electron"),r=require("child_process"),t=require("os"),o=require("fs"),a=require("path"),n=require("./playable-adapter-core-b6c0debc.js");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=i(r),c=i(t);const{exec:s,execSync:d}=l.default;var u={run:function(e,r){return s(e,(function(e,t,o){r&&r(e,t,o)}))},runSync:function(e){try{return{data:d(e).toString(),err:null,stderr:null}}catch(e){return{data:null,err:e.stderr.toString(),stderr:e.stderr.toString()}}}};const p="playable-ads-adapter",f=()=>{const e=`${Editor.Project.path}/.adapterrc`;return o.existsSync(e)?JSON.parse((r=e,o.readFileSync(r).toString(t))):null;var r,t},h=()=>{const e=Editor.Project.path,r="/build",t=f();let o=t?.buildPlatform??"web-mobile";return{projectRootPath:e,projectBuildPath:r,buildPlatform:o,originPkgPath:a.join(e,r,o),adapterBuildConfig:t}},g=()=>{const e=f();return!!e&&(e.skipBuild??!1)};var P=require("path").join(__dirname+"/3x-4f10520a.js");const m=e=>new Promise(((r,t)=>{let o=Editor.App.path;const a=(()=>{const e=c.default.platform();return"win32"===e?"WINDOWS":"darwin"===e?"MAC":e.toUpperCase()})();"MAC"===a?o=o.replace("/Resources/app.asar","/MacOS/CocosCreator"):"WINDOWS"===a?o=(e=>{let r=e;return-1!==r.indexOf("\\")&&(r=r.replace(/\\/g,"/")),r})(o).replace("/resources/app.asar","/CocosCreator.exe"):t(`不支持${a}平台构建`);u.run(`${o} --project ${Editor.Project.path} --build "platform=${e}"`,((e,t,o)=>{console.log(e,t,o),r()})).stdout.on("data",(e=>{console.log(e)}))})),j=async e=>{console.log(`${p} 进行预构建处理`),console.log(`${p} 跳过预构建处理`)},b=e=>new Promise((async(r,t)=>{const{projectRootPath:o,projectBuildPath:i,adapterBuildConfig:l}=h(),c=a.join(o,i);console.info(`${p} 开始适配，导出平台 ${e.platform}`);const s=(new Date).getTime(),d=()=>{const e=(new Date).getTime();console.log(`${p} 适配完成，共耗时${((e-s)/1e3).toFixed(0)}秒`),r(!0)},u=e=>{console.error("适配失败"),t(e)},f={buildFolderPath:c,adapterBuildConfig:{...l,buildPlatform:e.platform}};try{((e,r,t)=>{const{Worker:o}=require("worker_threads");console.log("支持Worker，将开启子线程适配"),new o(P,{workerData:e}).on("message",(({finished:e,msg:o,event:a})=>{"adapter:finished"!==a?console[a.split(":")[1]](o):e?r():t(o)}))})(f,d,u)}catch(e){console.log("不支持Worker，将开启主线程适配"),await n.exec3xAdapter(f,{mode:"serial"}),d()}}));exports.BUILDER_NAME=p,exports.builder3x=async()=>{try{const{buildPlatform:r,projectRootPath:t,projectBuildPath:o}=h();console.log(`开始构建项目，导出${r}包`);const n=g(),i=a.join(t,o);await j(),n||await m(r),await b({platform:r}),e.shell.openPath(i),console.log("构建完成")}catch(e){console.error(e)}},exports.initBuildFinishedEvent=b,exports.initBuildStartEvent=j;
