[{"__type__": "cc.animation.AnimationGraph", "_name": "", "_objFlags": 0, "_native": "", "_layers": [{"__id__": 1}], "_variables": {}}, {"__type__": "cc.animation.Layer", "_stateMachine": {"__id__": 2}, "name": "", "weight": 1, "mask": null}, {"__type__": "cc.animation.StateMachine", "__editorExtras__": {"name": "", "id": "17517658891700.08657033434138506", "clone": null, "viewport": {"scale": 0.8599999999999999, "top": 64.45734535458253, "left": -2.0763616766010715}}, "_states": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}, {"__id__": 12}, {"__id__": 14}], "_transitions": [{"__id__": 16}], "_entryState": {"__id__": 3}, "_exitState": {"__id__": 4}, "_anyState": {"__id__": 5}}, {"__type__": "cc.animation.State", "__editorExtras__": {"name": "", "id": "17517658891700.7942632854608049", "clone": null, "centerX": -125, "centerY": 0}, "name": "Entry"}, {"__type__": "cc.animation.State", "__editorExtras__": {"name": "", "id": "17517658891700.04501173969790262", "clone": null, "centerX": 125, "centerY": 0}, "name": "Exit"}, {"__type__": "cc.animation.State", "__editorExtras__": {"name": "", "id": "17517658891700.7680593749080307", "clone": null, "centerX": 125, "centerY": 0}, "name": "Any"}, {"__type__": "cc.animation.Motion", "__editorExtras__": {"name": "Idle", "id": "17517658954030.6425822282222582", "clone": null, "centerX": -53.5, "centerY": -94.984375}, "name": "Idle", "_components": [], "motion": {"__id__": 7}, "speed": 1, "speedMultiplier": "", "speedMultiplierEnabled": false}, {"__type__": "cc.animation.ClipMotion", "__editorExtras__": {"name": "", "id": "17517658954100.47577712100531877", "clone": null}, "clip": {"__uuid__": "668fc914-da7f-4397-811a-efe3c264b5bc@73b7f", "__expectedType__": "cc.AnimationClip"}}, {"__type__": "cc.animation.Motion", "__editorExtras__": {"name": "Intro", "id": "17517658954030.6486109225454828", "clone": null, "centerX": 136.5, "centerY": -115.984375}, "name": "Intro", "_components": [], "motion": {"__id__": 9}, "speed": 1, "speedMultiplier": "", "speedMultiplierEnabled": false}, {"__type__": "cc.animation.ClipMotion", "__editorExtras__": {"name": "", "id": "17517658954150.9179816981639086", "clone": null}, "clip": {"__uuid__": "dbdb9af1-b732-42bc-ac78-8034ad039ca1@73b7f", "__expectedType__": "cc.AnimationClip"}}, {"__type__": "cc.animation.Motion", "__editorExtras__": {"name": "", "id": "17517658954030.9929289802595334", "clone": null, "centerX": 190.5, "centerY": -55.984375}, "name": "Take 004", "_components": [], "motion": {"__id__": 11}, "speed": 1, "speedMultiplier": "", "speedMultiplierEnabled": false}, {"__type__": "cc.animation.ClipMotion", "__editorExtras__": {"name": "", "id": "17517658954160.5457139936453483", "clone": null}, "clip": {"__uuid__": "ee27550d-1fae-4e48-8075-6c91613b520a@73b7f", "__expectedType__": "cc.AnimationClip"}}, {"__type__": "cc.animation.Motion", "__editorExtras__": {"name": "", "id": "17517658954040.7091568623157765", "clone": null, "centerX": 201.5, "centerY": 43.015625}, "name": "Take 003", "_components": [], "motion": {"__id__": 13}, "speed": 1, "speedMultiplier": "", "speedMultiplierEnabled": false}, {"__type__": "cc.animation.ClipMotion", "__editorExtras__": {"name": "", "id": "17517658954150.6460804006375265", "clone": null}, "clip": {"__uuid__": "12a54b4c-3158-4adc-973b-90ed899efce3@73b7f", "__expectedType__": "cc.AnimationClip"}}, {"__type__": "cc.animation.Motion", "__editorExtras__": {"name": "", "id": "17517658954040.05938759658597337", "clone": null, "centerX": 349.5, "centerY": -53.984375}, "name": "Take 005", "_components": [], "motion": {"__id__": 15}, "speed": 1, "speedMultiplier": "", "speedMultiplierEnabled": false}, {"__type__": "cc.animation.ClipMotion", "__editorExtras__": {"name": "", "id": "17517658954190.37543002459703834", "clone": null}, "clip": {"__uuid__": "8318193d-84cb-4fa3-92db-31d411e36a4b@73b7f", "__expectedType__": "cc.AnimationClip"}}, {"__type__": "cc.animation.Transition", "__editorExtras__": null, "from": {"__id__": 3}, "to": {"__id__": 6}, "conditions": []}]