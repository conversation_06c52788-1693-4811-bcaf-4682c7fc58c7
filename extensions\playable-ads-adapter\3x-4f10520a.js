"use strict";var e=require("worker_threads"),r=require("./playable-adapter-core-b6c0debc.js");require("util"),require("stream"),require("path"),require("http"),require("https"),require("url"),require("fs"),require("assert"),require("zlib"),require("events");(async()=>{try{(()=>{const{log:r,info:t}=console;console.log=(...t)=>{e.parentPort?.postMessage({event:"adapter:log",msg:t.join(" ")}),r(...t)},console.info=(...r)=>{e.parentPort?.postMessage({event:"adapter:log",msg:r.join(" ")}),t(...r)}})();const{buildFolderPath:t,adapterBuildConfig:s}=e.workerData;await r.exec3xAdapter({buildFolderPath:t,adapterBuildConfig:s},{mode:"serial"}),e.parentPort?.postMessage({finished:!0,msg:"success",event:"adapter:finished"})}catch(r){e.parentPort?.postMessage({finished:!1,msg:r,event:"adapter:finished"})}})();
